2025-06-04 20:45:20,336 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:45:23,142 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:47:55,350 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:48:21,170 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:48:25,926 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:50:44,884 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:50:45,601 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:53:45,710 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:57:15,929 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:57:19,461 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:59:38,225 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:00:07,871 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:00:09,142 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:03:06,525 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:03:11,974 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:04:11,282 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:04:36,078 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:316]
2025-06-04 21:04:39,533 ERROR: 更新视频失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:337]
2025-06-04 21:05:26,615 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:316]
2025-06-04 21:05:26,772 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:316]
2025-06-04 21:13:46,122 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
