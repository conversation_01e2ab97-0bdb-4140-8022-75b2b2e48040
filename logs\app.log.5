2025-06-04 09:25:22,433 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,437 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,437 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 椒香青豌豆 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,437 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=389 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,438 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 砂窝蒜苔 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,438 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 砂窝蒜苔, ID=384 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,438 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 木耳炒藕片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,439 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 木耳炒藕片, ID=390 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,439 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 黄豆红烧肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,439 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=407 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,439 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,439 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,440 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 木耳炒藕片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,440 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 木耳炒藕片, ID=390 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,440 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 韭菜炒软饼元 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,440 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 韭菜炒软饼元, ID=379 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,440 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 豆腐汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,442 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 豆腐汤, ID=404 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,442 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 西红柿炒蛋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,442 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,442 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 猪肉汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,442 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 猪肉汤, ID=411 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,442 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,443 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,443 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,443 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,443 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 香干炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,444 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 香干炒肉, ID=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,444 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 清炒生菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,444 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 清炒生菜, ID=400 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,444 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 花菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,444 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 花菜炒肉, ID=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,445 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 三鲜菠菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,445 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 三鲜菠菜, ID=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,445 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,445 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,445 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,445 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,446 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 花菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,446 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 花菜炒肉, ID=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,446 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 芹菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,446 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 芹菜炒肉, ID=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,447 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 猪肉汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,447 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 猪肉汤, ID=411 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,447 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,447 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
