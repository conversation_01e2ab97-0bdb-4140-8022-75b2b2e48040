-- 全面的数据库健康检查脚本
-- 检查所有可能的问题，避免数据库损坏

PRINT '开始全面数据库健康检查...'
PRINT '========================================'

-- 1. 检查数据库基本状态
PRINT '1. 数据库基本状态检查：'
SELECT 
    name AS database_name,
    state_desc AS database_state,
    user_access_desc AS user_access,
    is_read_only,
    collation_name
FROM sys.databases 
WHERE name = DB_NAME()

-- 2. 检查所有关键表是否存在
PRINT ''
PRINT '2. 关键表存在性检查：'
SELECT 
    table_name,
    CASE WHEN OBJECT_ID(table_name) IS NOT NULL THEN '存在' ELSE '缺失' END AS status
FROM (VALUES 
    ('weekly_menus'),
    ('weekly_menu_recipes'),
    ('food_samples'),
    ('menu_plans'),
    ('recipes'),
    ('users'),
    ('administrative_areas')
) AS tables(table_name)

-- 3. 检查表的行数和基本统计
PRINT ''
PRINT '3. 表数据统计：'
SELECT 
    t.name AS table_name,
    p.rows AS row_count,
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS table_size_mb
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.name IN ('weekly_menus', 'weekly_menu_recipes', 'food_samples', 'menu_plans', 'recipes', 'users', 'administrative_areas')
    AND i.object_id > 255
GROUP BY t.name, p.rows
ORDER BY table_size_mb DESC

-- 4. 检查所有索引状态
PRINT ''
PRINT '4. 索引健康状态检查：'
SELECT 
    OBJECT_NAME(i.object_id) AS table_name,
    i.name AS index_name,
    i.type_desc AS index_type,
    i.is_disabled,
    i.is_hypothetical,
    i.has_filter,
    CASE 
        WHEN i.is_disabled = 1 THEN '已禁用'
        WHEN i.is_hypothetical = 1 THEN '假设索引'
        ELSE '正常'
    END AS index_status
FROM sys.indexes i
WHERE i.object_id IN (
    OBJECT_ID('weekly_menus'),
    OBJECT_ID('weekly_menu_recipes'), 
    OBJECT_ID('food_samples'),
    OBJECT_ID('menu_plans'),
    OBJECT_ID('recipes'),
    OBJECT_ID('users'),
    OBJECT_ID('administrative_areas')
)
    AND i.name IS NOT NULL
ORDER BY table_name, index_name

-- 5. 检查索引碎片情况
PRINT ''
PRINT '5. 索引碎片检查（>30%需要重建）：'
SELECT 
    OBJECT_NAME(ips.object_id) AS table_name,
    i.name AS index_name,
    ips.index_type_desc,
    ips.avg_fragmentation_in_percent,
    ips.page_count,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 THEN '需要重建'
        WHEN ips.avg_fragmentation_in_percent > 10 THEN '需要重组'
        ELSE '良好'
    END AS fragmentation_status
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.object_id IN (
    OBJECT_ID('weekly_menus'),
    OBJECT_ID('weekly_menu_recipes'),
    OBJECT_ID('food_samples'),
    OBJECT_ID('menu_plans'),
    OBJECT_ID('recipes'),
    OBJECT_ID('users'),
    OBJECT_ID('administrative_areas')
)
    AND ips.page_count > 100  -- 只检查有足够页面的索引
ORDER BY ips.avg_fragmentation_in_percent DESC

-- 6. 检查外键约束状态
PRINT ''
PRINT '6. 外键约束检查：'
SELECT 
    OBJECT_NAME(f.parent_object_id) AS table_name,
    f.name AS foreign_key_name,
    OBJECT_NAME(f.referenced_object_id) AS referenced_table,
    f.is_disabled,
    f.is_not_trusted,
    CASE 
        WHEN f.is_disabled = 1 THEN '已禁用'
        WHEN f.is_not_trusted = 1 THEN '不可信'
        ELSE '正常'
    END AS constraint_status
FROM sys.foreign_keys f
WHERE f.parent_object_id IN (
    OBJECT_ID('weekly_menus'),
    OBJECT_ID('weekly_menu_recipes'),
    OBJECT_ID('food_samples'),
    OBJECT_ID('menu_plans'),
    OBJECT_ID('recipes'),
    OBJECT_ID('users'),
    OBJECT_ID('administrative_areas')
)
ORDER BY table_name, foreign_key_name

-- 7. 检查统计信息更新状态
PRINT ''
PRINT '7. 统计信息状态检查：'
SELECT 
    OBJECT_NAME(s.object_id) AS table_name,
    s.name AS stats_name,
    s.stats_id,
    sp.last_updated,
    sp.rows,
    sp.modification_counter,
    CASE 
        WHEN sp.last_updated < DATEADD(day, -7, GETDATE()) THEN '需要更新'
        WHEN sp.modification_counter > sp.rows * 0.2 THEN '需要更新'
        ELSE '正常'
    END AS stats_status
FROM sys.stats s
CROSS APPLY sys.dm_db_stats_properties(s.object_id, s.stats_id) sp
WHERE s.object_id IN (
    OBJECT_ID('weekly_menus'),
    OBJECT_ID('weekly_menu_recipes'),
    OBJECT_ID('food_samples'),
    OBJECT_ID('menu_plans'),
    OBJECT_ID('recipes'),
    OBJECT_ID('users'),
    OBJECT_ID('administrative_areas')
)
ORDER BY table_name, stats_name

-- 8. 检查数据库错误日志
PRINT ''
PRINT '8. 检查最近的数据库错误：'
EXEC xp_readerrorlog 0, 1, N'error', NULL, NULL, NULL, N'DESC'

-- 9. 检查阻塞和死锁情况
PRINT ''
PRINT '9. 当前阻塞会话检查：'
SELECT 
    session_id,
    blocking_session_id,
    wait_type,
    wait_time,
    wait_resource,
    command,
    status
FROM sys.dm_exec_requests
WHERE blocking_session_id <> 0

-- 10. 检查数据库文件大小和增长
PRINT ''
PRINT '10. 数据库文件状态检查：'
SELECT 
    name AS file_name,
    type_desc AS file_type,
    size * 8 / 1024 AS size_mb,
    max_size * 8 / 1024 AS max_size_mb,
    growth AS growth_setting,
    is_percent_growth,
    physical_name
FROM sys.database_files

PRINT ''
PRINT '========================================'
PRINT '数据库健康检查完成！'
PRINT '========================================'
PRINT ''
PRINT '⚠️  请检查以上结果中的问题：'
PRINT '1. 表缺失 - 严重问题'
PRINT '2. 索引已禁用 - 性能问题'
PRINT '3. 外键约束问题 - 数据完整性问题'
PRINT '4. 索引碎片过高 - 性能问题'
PRINT '5. 统计信息过期 - 查询优化问题'
PRINT '6. 数据库错误 - 系统问题'
PRINT '7. 阻塞会话 - 并发问题'
PRINT ''
PRINT '✅ 如果所有检查都正常，可以安全执行优化'
PRINT '❌ 如果发现问题，建议先修复再优化'
PRINT '========================================'
