2025-06-04 09:25:00,498 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-04 09:25:00,502 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:00,502 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:00,503 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 三鲜菠菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:00,503 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 三鲜菠菜, ID=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:00,503 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:00,504 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=404 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:00,504 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 芹菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:00,504 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 芹菜炒肉, ID=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:00,504 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 剁椒笋片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:00,505 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 剁椒笋片, ID=393 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:00,505 INFO: 主表数据补全完成，准备保存: 总菜品数=90, 已补全=90, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-04 09:25:00,517 INFO: 删除现有菜单食谱(主表): weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-04 09:25:00,523 INFO: 删除现有菜单食谱(副表): weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-04 09:25:00,523 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-04 09:25:00,743 INFO: 保存周菜单成功(主表和副表): id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-04 09:25:00,744 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 09:25:22,390 INFO: 获取副表数据用于补全主表: weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-04 09:25:22,394 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 花菜炒肉, ID=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,394 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,395 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,395 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 茄子烧豆角, ID=383 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,395 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,395 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,396 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 三鲜菠菜, ID=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,396 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=404 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,396 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 芹菜炒肉, ID=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,396 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 剁椒笋片, ID=393 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,396 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=399 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,397 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 香干炒肉, ID=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,397 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 砂窝蒜苔, ID=384 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,397 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 小白菜炖豆腐, ID=392 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,397 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,397 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,398 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 麻婆豆腐, ID=406 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,398 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,398 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,398 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 手撕包菜, ID=391 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,398 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 清炒生菜, ID=400 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,398 INFO: 副表数据: 日期=2, 餐次=早餐, 菜品=🏫 长豇豆烧茄子, ID=380 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,399 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,399 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 椒香青豌豆, ID=389 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,399 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 砂窝蒜苔, ID=384 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,399 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 木耳炒藕片, ID=390 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,400 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=407 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,400 INFO: 副表数据: 日期=2, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,400 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 木耳炒藕片, ID=390 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,400 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 韭菜炒软饼元, ID=379 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:22,400 INFO: 副表数据: 日期=2, 餐次=晚餐, 菜品=🏫 豆腐汤, ID=404 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
