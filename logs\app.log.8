2025-06-04 09:25:22,447 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,453 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,453 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 炒大白菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,453 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=399 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,454 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 长豇豆烧茄子 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,454 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 长豇豆烧茄子, ID=380 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,454 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,454 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,455 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,455 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,455 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,455 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,455 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 西红柿炒蛋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,456 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,456 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 三鲜菠菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,456 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 三鲜菠菜, ID=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,456 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 剁椒土豆丝元 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,456 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 剁椒土豆丝元, ID=394 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,457 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,457 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,457 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,457 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,458 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 黄豆红烧肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,458 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 黄豆红烧肉, ID=407 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,458 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 花菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,458 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 花菜炒肉, ID=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,458 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 猪肉汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,459 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 猪肉汤, ID=411 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,459 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 香干炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,459 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 香干炒肉, ID=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,459 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 西红柿炒蛋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,459 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,460 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 小白菜炖豆腐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,460 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 小白菜炖豆腐, ID=392 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,460 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 麻婆豆腐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,460 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 麻婆豆腐, ID=406 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,461 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 猪肉汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,461 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 猪肉汤, ID=411 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,461 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,461 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,461 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,462 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,462 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 剁椒笋片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,462 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 剁椒笋片, ID=393 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,462 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,462 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
