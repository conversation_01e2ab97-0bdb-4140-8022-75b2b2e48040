2025-06-04 17:24:54,593 INFO: 转换后的日期对象: 2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 17:24:54,593 INFO: 计算的周结束日期: 2025-06-15 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 17:24:54,609 INFO: 检查是否已存在该周的菜单: area_id=45, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 17:24:54,609 INFO: 获取周菜单: area_id=45, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 17:24:54,609 INFO: 使用优化后的查询: area_id=45, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 17:24:54,609 INFO: 执行主SQL查询: area_id=45, week_start_str=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-04 17:24:54,625 INFO: 主查询未找到菜单: area_id=45, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-04 17:24:54,625 INFO: 使用日期字符串: week_start_str=2025-06-09, week_end_str=2025-06-15 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-04 17:24:54,625 INFO: 准备执行SQL创建菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-04 17:24:54,625 INFO: SQL参数: {'area_id': '45', 'week_start_str': '2025-06-09', 'week_end_str': '2025-06-15', 'status': '计划中', 'created_by': 37} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-04 17:24:54,625 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-04 17:24:54,625 INFO: SQL执行成功，获取到ID: 40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-04 17:24:54,625 INFO: 检查数据库连接状态 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-04 17:24:54,625 INFO: 数据库连接正常 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-04 17:24:54,625 INFO: 事务提交成功 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-04 17:24:54,625 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 17:24:54,625 INFO: 验证成功: 菜单已创建 ID=40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-04 17:24:54,625 INFO: 周菜单创建成功: id=40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 17:24:54,641 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 40, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 17:25:55,745 INFO: 获取副表数据用于补全主表: weekly_menu_id=40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-04 17:25:55,745 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-04 17:25:55,745 INFO: 主表数据补全完成，准备保存: 总菜品数=15, 已补全=15, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-04 17:25:55,755 INFO: 删除现有菜单食谱(主表): weekly_menu_id=40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-04 17:25:55,755 INFO: 删除现有菜单食谱(副表): weekly_menu_id=40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-04 17:25:55,777 INFO: 保存周菜单成功(主表和副表): id=40 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-04 17:25:55,777 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 17:27:33,200 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:27:33,200 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 17:28:37,925 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:28:37,931 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 17:29:46,506 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:29:56,960 WARNING: Suspicious path blocked: /admin/users from 222.242.228.97 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 17:29:56,960 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:29:59,613 WARNING: Suspicious path blocked: /admin/dashboard from 222.242.228.97 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 17:29:59,613 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:30:28,434 WARNING: Suspicious path blocked: /admin/users from 222.242.228.97 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 17:30:28,434 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:30:47,617 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:30:47,617 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 17:30:56,047 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:30:56,047 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 17:31:25,136 ERROR: 周菜单操作异常: time data 'undefined' does not match format '%Y-%m-%d' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py:115]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py", line 104, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 130, in plan
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data 'undefined' does not match format '%Y-%m-%d'
2025-06-04 17:32:03,688 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:32:03,694 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 17:32:10,444 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-02"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 17:32:10,444 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 17:32:10,444 INFO: 检查用户权限: user_id=36, area_id=44 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
