2025-06-04 15:01:39,783 WARNING: Suspicious path blocked: /.env.example from 78.153.140.179 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 15:01:39,799 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 15:01:42,201 WARNING: Suspicious path blocked: /backend/.env from 78.153.140.179 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 15:01:42,201 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 15:01:42,986 WARNING: Suspicious path blocked: /application/.env from 78.153.140.179 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 15:01:42,986 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 15:01:43,671 WARNING: Suspicious path blocked: /sendgrid/.env from 78.153.140.179 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 15:01:43,671 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 15:01:45,026 WARNING: Suspicious path blocked: /dashboard/.env from 78.153.140.179 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 15:01:45,026 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 15:01:46,455 WARNING: Suspicious path blocked: /services/.env from 78.153.140.179 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 15:01:46,455 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 15:23:11,614 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 15:23:11,630 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 15:24:09,794 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-04 15:25:17,778 INFO: 查看库存详情: ID=97, 批次号=B2025060452b0b5 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-04 15:33:02,575 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-04 15:33:04,339 INFO: 库存统计页面：为区域 [45] 找到 5 个关联供应商 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:964]
2025-06-04 15:33:16,634 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 15:33:16,634 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 15:33:49,855 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 15:33:49,872 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 16:04:07,998 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 16:04:07,998 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 16:05:53,183 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 16:05:53,183 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 16:10:35,902 INFO: 复制食谱 - 原食谱ID: 325, 名称: 酱爆刀豆 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-04 16:10:35,902 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-04 16:10:35,902 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-04 16:12:11,812 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-04 16:12:14,203 INFO: 使用用户当前区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\routes.py:2853]
2025-06-04 16:12:14,203 INFO: 使用现有二维码: companion_school_42_entry.png [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:129]
2025-06-04 16:12:14,203 INFO: 生成入口二维码，URL: http://tdtech.xin/daily-management/public/companions/entry/42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\routes.py:2876]
2025-06-04 16:12:14,218 INFO: 使用现有二维码: companion_school_42_direct.png [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:129]
2025-06-04 16:12:14,218 INFO: 生成直接添加二维码，URL: http://tdtech.xin/daily-management/public/companions/add/42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\routes.py:2882]
2025-06-04 16:58:44,392 WARNING: Suspicious path blocked: /.env.backup from 196.251.85.66 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 16:58:44,392 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:03:08,467 WARNING: Suspicious path blocked: /.git/config from 196.251.66.177 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 17:03:08,467 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:10:00,725 WARNING: Suspicious path blocked: /.env_1 from 196.251.85.74 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 17:10:00,725 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:24:03,505 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 17:24:03,505 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 17:24:54,593 INFO: 收到创建周菜单请求: b'{"area_id":"45","week_start":"2025-06-09"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 17:24:54,593 INFO: 创建周菜单参数: area_id=45, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 17:24:54,593 INFO: 检查用户权限: user_id=37, area_id=45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 17:24:54,593 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 17:24:54,593 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 17:24:54,593 INFO: 开始创建周菜单: area_id=45, week_start=2025-06-09, created_by=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 17:24:54,593 INFO: 开始创建周菜单: area_id=45, week_start=2025-06-09, created_by=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
