2025-06-04 09:07:45,458 ERROR: 创建周菜单过程中发生异常: 获取周菜单失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start >= ? AND weekly_menus.week_start < ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0), datetime.datetime(2025, 6, 3, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:264]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py", line 69, in get_menu
    menu = WeeklyMenu.query.filter(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2728, in first
    return self.limit(1)._iter().first()  # type: ignore
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start = ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.Error: ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py", line 95, in get_menu
    menu = WeeklyMenu.query.filter(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2728, in first
    return self.limit(1)._iter().first()  # type: ignore
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.DBAPIError: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start >= ? AND weekly_menus.week_start < ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0), datetime.datetime(2025, 6, 3, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py", line 156, in create_menu
    existing_menu = WeeklyMenuService.get_menu(area_id, week_start_date)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py", line 118, in get_menu
    raise WeeklyMenuError(f"获取周菜单失败: {str(e)}")
app.services.weekly_menu_service.WeeklyMenuError: 获取周菜单失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start >= ? AND weekly_menus.week_start < ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0), datetime.datetime(2025, 6, 3, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
