-- 从备份表恢复原始单位数据
-- 仅在需要回滚时执行此脚本

PRINT '=== 开始从备份恢复原始单位数据 ===';

-- 检查备份表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ingredients_units_backup')
BEGIN
    PRINT '❌ 错误：未找到备份表 ingredients_units_backup';
    PRINT '请先执行 backup_units_before_update.sql 创建备份';
    RETURN;
END

PRINT '✓ 发现备份表，开始恢复操作...';

BEGIN TRANSACTION;

BEGIN TRY
    DECLARE @total_restored INT = 0;
    DECLARE @table_restored INT;
    
    -- 1. 恢复食材表的单位数据
    UPDATE i 
    SET unit = b.original_unit,
        standard_unit = b.original_standard_unit,
        updated_at = GETDATE()
    FROM ingredients i
    INNER JOIN ingredients_units_backup b ON i.id = b.id;
    
    SET @table_restored = @@ROWCOUNT;
    SET @total_restored = @total_restored + @table_restored;
    PRINT '✓ 食材表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    
    -- 2. 恢复采购订单项表的单位数据
    UPDATE poi 
    SET unit = b.original_unit,
        updated_at = GETDATE()
    FROM purchase_order_items poi
    INNER JOIN purchase_order_items_units_backup b ON poi.id = b.id;
    
    SET @table_restored = @@ROWCOUNT;
    SET @total_restored = @total_restored + @table_restored;
    PRINT '✓ 采购订单项表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    
    -- 3. 恢复库存表的单位数据
    UPDATE inv 
    SET unit = b.original_unit
    FROM inventories inv
    INNER JOIN inventories_units_backup b ON inv.id = b.id;
    
    SET @table_restored = @@ROWCOUNT;
    SET @total_restored = @total_restored + @table_restored;
    PRINT '✓ 库存表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    
    -- 4. 恢复入库明细表的单位数据
    UPDATE sii 
    SET unit = b.original_unit,
        updated_at = GETDATE()
    FROM stock_in_items sii
    INNER JOIN stock_in_items_units_backup b ON sii.id = b.id;
    
    SET @table_restored = @@ROWCOUNT;
    SET @total_restored = @total_restored + @table_restored;
    PRINT '✓ 入库明细表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    
    -- 5. 恢复出库明细表的单位数据（如果存在）
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items_units_backup')
    BEGIN
        UPDATE soi 
        SET unit = b.original_unit,
            updated_at = GETDATE()
        FROM stock_out_items soi
        INNER JOIN stock_out_items_units_backup b ON soi.id = b.id;
        
        SET @table_restored = @@ROWCOUNT;
        SET @total_restored = @total_restored + @table_restored;
        PRINT '✓ 出库明细表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    END
    
    -- 6. 恢复消耗明细表的单位数据
    UPDATE cd 
    SET unit = b.original_unit,
        updated_at = GETDATE()
    FROM consumption_details cd
    INNER JOIN consumption_details_units_backup b ON cd.id = b.id;
    
    SET @table_restored = @@ROWCOUNT;
    SET @total_restored = @total_restored + @table_restored;
    PRINT '✓ 消耗明细表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    
    -- 7. 恢复食谱食材表的单位数据
    UPDATE ri 
    SET unit = b.original_unit
    FROM recipe_ingredients ri
    INNER JOIN recipe_ingredients_units_backup b ON ri.recipe_id = b.recipe_id AND ri.ingredient_id = b.ingredient_id;
    
    SET @table_restored = @@ROWCOUNT;
    SET @total_restored = @total_restored + @table_restored;
    PRINT '✓ 食谱食材表: 已恢复 ' + CAST(@table_restored AS VARCHAR(10)) + ' 条记录';
    
    -- 提交事务
    COMMIT TRANSACTION;
    PRINT '';
    PRINT '✅ 所有恢复操作完成！总共恢复了 ' + CAST(@total_restored AS VARCHAR(10)) + ' 条记录';
    PRINT '✅ 事务提交成功';
    
END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION;
    PRINT '';
    PRINT '❌ 恢复失败，所有操作已回滚: ' + ERROR_MESSAGE();
    THROW;
END CATCH

PRINT '';
PRINT '3. 验证恢复结果：';

-- 验证恢复结果，显示各表的单位分布
PRINT '恢复后各表的单位分布：';

PRINT '食材表单位分布：';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM ingredients 
WHERE status = 1
GROUP BY unit
ORDER BY COUNT(*) DESC;

PRINT '采购订单项表单位分布：';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM purchase_order_items
GROUP BY unit
ORDER BY COUNT(*) DESC;

PRINT '库存表单位分布：';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM inventories
GROUP BY unit
ORDER BY COUNT(*) DESC;

PRINT '';
PRINT '=== 单位数据恢复完成 ===';
PRINT '';
PRINT '📋 后续建议：';
PRINT '1. 测试各项功能是否恢复正常';
PRINT '2. 检查前端页面显示是否正确';
PRINT '3. 如果确认恢复成功，可以删除备份表：';
PRINT '   DROP TABLE ingredients_units_backup;';
PRINT '   DROP TABLE purchase_order_items_units_backup;';
PRINT '   DROP TABLE inventories_units_backup;';
PRINT '   DROP TABLE stock_in_items_units_backup;';
PRINT '   DROP TABLE consumption_details_units_backup;';
PRINT '   DROP TABLE recipe_ingredients_units_backup;';
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items_units_backup')
    PRINT '   DROP TABLE stock_out_items_units_backup;';
