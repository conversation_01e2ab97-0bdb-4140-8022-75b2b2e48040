2025-06-04 09:25:22,463 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 炒大白菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,467 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 炒大白菜, ID=399 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,467 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 麻婆豆腐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,468 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 麻婆豆腐, ID=406 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,468 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,469 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,469 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 西红柿炒蛋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,469 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,469 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 小白菜炖豆腐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,469 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 小白菜炖豆腐, ID=392 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,470 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 木耳炒藕片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,470 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 木耳炒藕片, ID=390 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,470 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 砂窝蒜苔 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,470 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 砂窝蒜苔, ID=384 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,470 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 芹菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,471 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 芹菜炒肉, ID=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,471 INFO: 主表数据补全完成，准备保存: 总菜品数=157, 已补全=157, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-04 09:25:22,475 INFO: 删除现有菜单食谱(主表): weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-04 09:25:22,477 INFO: 删除现有菜单食谱(副表): weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-04 09:25:22,477 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-04 09:25:22,478 WARNING: 跳过无效日期: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-04 09:25:22,478 WARNING: 跳过无效日期: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-04 09:25:22,478 WARNING: 跳过无效日期: 4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-04 09:25:22,478 WARNING: 跳过无效日期: 5 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-04 09:25:22,560 INFO: 保存周菜单成功(主表和副表): id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-04 09:25:22,560 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 09:25:28,253 INFO: 发布周菜单成功: id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-04 09:25:32,692 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 09:25:32,706 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 09:54:24,377 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 09:54:24,389 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 09:54:44,417 INFO: 更新了日期 2025-06-02 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-04 09:54:44,426 INFO: 更新了日期 2025-06-03 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-04 09:54:44,433 INFO: 更新了日期 2025-06-04 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-04 09:54:44,439 INFO: 更新了日期 2025-06-05 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-04 09:54:44,444 INFO: 更新了日期 2025-06-06 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-04 10:01:34,034 INFO: 用户 rwhxx2333 正在上传入库单据，不进行权限检查 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2209]
2025-06-04 10:02:19,048 INFO: 用户 rwhxx2333 正在上传入库单据，不进行权限检查 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2209]
2025-06-04 10:02:19,059 INFO: 文档 19 关联了 3 个批次 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2334]
2025-06-04 10:03:34,749 INFO: 用户 rwhxx2333 正在上传入库单据，不进行权限检查 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2209]
2025-06-04 10:03:34,771 INFO: 文档 20 关联了 16 个批次 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2334]
2025-06-04 10:15:38,079 INFO: 批次编辑器保存 - 入库单ID: 95 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:921]
2025-06-04 10:15:38,080 INFO: 选中的项目: ['229', '231'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:922]
2025-06-04 10:15:38,080 INFO: 单价字段 - unit_price_229: 5.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-04 10:15:38,080 INFO: 单价字段 - unit_price_231: 8 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:925]
2025-06-04 10:15:38,084 INFO: 项目 229 详细信息: [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-04 10:15:38,084 INFO:   - 供应商ID: 29 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-04 10:15:38,085 INFO:   - 存储位置ID: 18 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-04 10:15:38,085 INFO:   - 数量: 10000.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:955]
2025-06-04 10:15:38,085 INFO:   - 单价: 5.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:956]
2025-06-04 10:15:38,085 INFO:   - 生产日期: 2025-06-04 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:957]
2025-06-04 10:15:38,085 INFO:   - 过期日期: 2025-12-01 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:958]
2025-06-04 10:15:38,101 INFO: 项目 231 详细信息: [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-04 10:15:38,101 INFO:   - 供应商ID: 29 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-04 10:15:38,101 INFO:   - 存储位置ID: 18 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:954]
