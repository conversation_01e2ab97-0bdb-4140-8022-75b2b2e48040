-- 完善的索引检查脚本
PRINT '检查食品留样管理相关表的索引详细信息...'
PRINT '========================================'

-- 1. 检查 food_samples 表的详细索引信息
PRINT '1. food_samples 表的索引详情：'
SELECT
    i.name AS index_name,
    i.type_desc AS index_type,
    i.is_unique,
    i.is_primary_key,
    STUFF((
        SELECT ', ' + c.name + CASE WHEN ic.is_descending_key = 1 THEN ' DESC' ELSE '' END
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS key_columns,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 1
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS included_columns
FROM sys.indexes i
WHERE i.object_id = OBJECT_ID('food_samples')
    AND i.name IS NOT NULL
ORDER BY i.name

PRINT ''
PRINT '2. 各表索引总数统计：'
SELECT
    OBJECT_NAME(i.object_id) AS table_name,
    COUNT(*) AS total_indexes,
    SUM(CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END) AS primary_key_count,
    SUM(CASE WHEN i.is_unique = 1 AND i.is_primary_key = 0 THEN 1 ELSE 0 END) AS unique_indexes,
    SUM(CASE WHEN i.is_unique = 0 AND i.is_primary_key = 0 THEN 1 ELSE 0 END) AS non_unique_indexes
FROM sys.indexes i
WHERE i.object_id IN (
    OBJECT_ID('food_samples'),
    OBJECT_ID('menu_plans'),
    OBJECT_ID('recipes'),
    OBJECT_ID('users'),
    OBJECT_ID('administrative_areas')
)
    AND i.name IS NOT NULL
GROUP BY OBJECT_NAME(i.object_id)
ORDER BY total_indexes DESC

PRINT ''
PRINT '3. 我们创建的索引（包含safe/fixed/final后缀）：'
SELECT
    OBJECT_NAME(i.object_id) AS table_name,
    i.name AS index_name,
    i.type_desc AS index_type,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS key_columns
FROM sys.indexes i
WHERE i.object_id IN (
    OBJECT_ID('food_samples'),
    OBJECT_ID('menu_plans'),
    OBJECT_ID('recipes'),
    OBJECT_ID('users'),
    OBJECT_ID('administrative_areas')
)
    AND (i.name LIKE '%safe%' OR i.name LIKE '%fixed%' OR i.name LIKE '%final%')
ORDER BY table_name, index_name

PRINT ''
PRINT '4. 检查索引使用统计（如果有查询活动）：'
SELECT
    OBJECT_NAME(s.object_id) AS table_name,
    i.name AS index_name,
    s.user_seeks AS 查找次数,
    s.user_scans AS 扫描次数,
    s.user_lookups AS 查询次数,
    s.user_updates AS 更新次数,
    CASE
        WHEN s.user_seeks + s.user_scans + s.user_lookups = 0 THEN '未使用'
        WHEN s.user_updates > (s.user_seeks + s.user_scans + s.user_lookups) * 2 THEN '更新过多'
        ELSE '正常使用'
    END AS 使用状态
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE s.database_id = DB_ID()
    AND OBJECT_NAME(s.object_id) IN ('food_samples', 'menu_plans', 'recipes', 'users', 'administrative_areas')
    AND i.name IS NOT NULL
ORDER BY table_name, 使用状态, index_name

PRINT ''
PRINT '5. 检查可能重复的索引键列：'
SELECT
    t1.table_name,
    t1.index_name AS index1,
    t2.index_name AS index2,
    t1.key_columns
FROM (
    SELECT
        OBJECT_NAME(i.object_id) AS table_name,
        i.name AS index_name,
        STUFF((
            SELECT ', ' + c.name
            FROM sys.index_columns ic
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
            ORDER BY ic.key_ordinal
            FOR XML PATH('')
        ), 1, 2, '') AS key_columns
    FROM sys.indexes i
    WHERE i.object_id IN (OBJECT_ID('food_samples'), OBJECT_ID('menu_plans'))
        AND i.name IS NOT NULL
) t1
INNER JOIN (
    SELECT
        OBJECT_NAME(i.object_id) AS table_name,
        i.name AS index_name,
        STUFF((
            SELECT ', ' + c.name
            FROM sys.index_columns ic
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
            ORDER BY ic.key_ordinal
            FOR XML PATH('')
        ), 1, 2, '') AS key_columns
    FROM sys.indexes i
    WHERE i.object_id IN (OBJECT_ID('food_samples'), OBJECT_ID('menu_plans'))
        AND i.name IS NOT NULL
) t2 ON t1.table_name = t2.table_name
    AND t1.key_columns = t2.key_columns
    AND t1.index_name < t2.index_name

PRINT ''
PRINT '========================================'
PRINT '索引检查完成！'
PRINT '========================================'
PRINT ''
PRINT '📊 分析建议：'
PRINT '1. 如果 food_samples 表索引超过 8 个，建议清理'
PRINT '2. 如果发现"未使用"的索引，可以考虑删除'
PRINT '3. 如果发现"更新过多"的索引，说明写入负担重'
PRINT '4. 如果发现重复的索引键列，应该删除重复索引'
PRINT '5. 关注索引的 key_columns 和 included_columns 设计'
PRINT ''
PRINT '🚀 性能优化建议：'
PRINT '• 保留最常用的查询索引'
PRINT '• 删除重复和很少使用的索引'
PRINT '• 定期更新统计信息'
PRINT '• 监控查询执行计划'
PRINT '========================================'
