# 食材单位统一更新为公斤 - 执行指南

## 概述
本指南用于将食材库表及相关表中的所有食材单位统一更新为"公斤"。

## 涉及的表
- `ingredients` - 食材表
- `purchase_order_items` - 采购订单项表
- `inventories` - 库存表
- `stock_in_items` - 入库明细表
- `stock_out_items` - 出库明细表（如果存在）
- `consumption_details` - 消耗明细表
- `recipe_ingredients` - 食谱食材表

## 执行步骤

### 1. 准备工作
- ⚠️ **重要：在执行前请备份整个数据库**
- 建议在业务低峰期执行
- 通知相关用户系统将进行维护

### 2. 执行顺序

#### 步骤1：备份当前单位数据
```sql
-- 执行备份脚本
sqlcmd -S 服务器名 -d 数据库名 -i backup_units_before_update.sql
```
或在 SQL Server Management Studio 中执行 `backup_units_before_update.sql`

#### 步骤2：检查当前单位分布（可选）
```sql
-- 执行检查脚本
sqlcmd -S 服务器名 -d 数据库名 -i update_ingredient_units_to_kg.sql
```
这个脚本只更新食材表，可以先执行看看效果。

#### 步骤3：统一更新所有表的单位
```sql
-- 执行完整更新脚本
sqlcmd -S 服务器名 -d 数据库名 -i update_all_units_to_kg.sql
```

### 3. 验证结果
执行完成后，检查以下内容：
- [ ] 所有食材的单位都显示为"公斤"
- [ ] 采购订单功能正常
- [ ] 库存管理功能正常
- [ ] 入库出库功能正常
- [ ] 消耗计划功能正常
- [ ] 食谱管理功能正常
- [ ] 前端页面显示正确

### 4. 如需回滚
如果更新后发现问题，可以执行回滚：
```sql
-- 执行回滚脚本
sqlcmd -S 服务器名 -d 数据库名 -i restore_units_from_backup.sql
```

### 5. 清理备份表（可选）
确认更新成功且系统运行正常后，可以删除备份表：
```sql
DROP TABLE ingredients_units_backup;
DROP TABLE purchase_order_items_units_backup;
DROP TABLE inventories_units_backup;
DROP TABLE stock_in_items_units_backup;
DROP TABLE consumption_details_units_backup;
DROP TABLE recipe_ingredients_units_backup;
-- 如果存在出库明细备份表
-- DROP TABLE stock_out_items_units_backup;
```

## 注意事项

### 执行前
1. **数据库备份**：务必完整备份数据库
2. **业务影响**：在业务低峰期执行，避免影响正常使用
3. **用户通知**：提前通知用户系统维护时间

### 执行中
1. **监控执行**：密切关注脚本执行过程
2. **错误处理**：如果出现错误，脚本会自动回滚
3. **执行时间**：根据数据量大小，执行时间可能较长

### 执行后
1. **功能测试**：全面测试相关功能
2. **数据验证**：检查数据一致性
3. **用户反馈**：收集用户使用反馈
4. **性能监控**：观察系统性能是否正常

## 常见问题

### Q: 更新会影响历史数据吗？
A: 会更新所有相关表的单位字段，但不会影响数量等其他数据。

### Q: 如果执行过程中出错怎么办？
A: 脚本使用事务机制，出错会自动回滚。如果需要，可以使用备份数据恢复。

### Q: 更新后前端显示会有问题吗？
A: 理论上不会，但建议测试所有相关功能页面。

### Q: 可以只更新部分表吗？
A: 不建议，为了保持数据一致性，应该统一更新所有相关表。

## 联系支持
如果在执行过程中遇到问题，请联系技术支持团队。
