2025-06-04 09:25:22,419 WARNING: 跳过无效日期: 4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-04 09:25:22,423 WARNING: 跳过无效日期: 5 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-04 09:25:22,423 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 花菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,423 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 花菜炒肉, ID=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,424 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,424 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,424 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 西红柿炒蛋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,425 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,425 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 茄子烧豆角 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,425 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 茄子烧豆角, ID=383 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,425 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,425 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,426 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,426 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,426 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 三鲜菠菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,426 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 三鲜菠菜, ID=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,427 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,427 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=404 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,427 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 芹菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,427 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 芹菜炒肉, ID=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,427 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 剁椒笋片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,428 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 剁椒笋片, ID=393 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,428 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 炒大白菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,428 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=399 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,428 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 香干炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,428 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 香干炒肉, ID=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,429 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 砂窝蒜苔 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,429 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 砂窝蒜苔, ID=384 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,429 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 小白菜炖豆腐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,429 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 小白菜炖豆腐, ID=392 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,429 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,429 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,430 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,430 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,430 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 麻婆豆腐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,430 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 麻婆豆腐, ID=406 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,431 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,431 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,431 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 蒸蛋羹 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,431 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,431 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 手撕包菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,432 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 手撕包菜, ID=391 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,432 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 清炒生菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,432 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 清炒生菜, ID=400 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-04 09:25:22,432 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 长豇豆烧茄子 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:389]
2025-06-04 09:25:22,432 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 长豇豆烧茄子, ID=380 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
