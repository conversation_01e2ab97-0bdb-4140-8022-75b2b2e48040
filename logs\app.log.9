2025-06-04 09:15:45,333 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-04 09:17:09,364 INFO: 收到创建周菜单请求: b'{"area_id":"45","week_start":"2025-06-02"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 09:17:09,364 INFO: 创建周菜单参数: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 09:17:09,365 INFO: 检查用户权限: user_id=37, area_id=45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 09:17:09,365 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 09:17:09,372 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 09:17:09,372 INFO: 开始创建周菜单: area_id=45, week_start=2025-06-02, created_by=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 09:17:09,372 INFO: 开始创建周菜单: area_id=45, week_start=2025-06-02, created_by=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-04 09:17:09,373 INFO: 转换后的日期对象: 2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 09:17:09,373 INFO: 计算的周结束日期: 2025-06-08 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 09:17:09,373 INFO: 检查是否已存在该周的菜单: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 09:17:09,373 INFO: 获取周菜单: area_id=45, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 09:17:09,373 INFO: 使用优化后的查询: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 09:17:09,374 INFO: 执行主SQL查询: area_id=45, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-04 09:17:09,377 INFO: 主查询未找到菜单: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-04 09:17:09,377 INFO: 使用日期字符串: week_start_str=2025-06-02, week_end_str=2025-06-08 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-04 09:17:09,377 INFO: 准备执行SQL创建菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-04 09:17:09,377 INFO: SQL参数: {'area_id': '45', 'week_start_str': '2025-06-02', 'week_end_str': '2025-06-08', 'status': '计划中', 'created_by': 37} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-04 09:17:09,378 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-04 09:17:09,381 INFO: SQL执行成功，获取到ID: 39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-04 09:17:09,381 INFO: 检查数据库连接状态 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-04 09:17:09,382 INFO: 数据库连接正常 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-04 09:17:09,383 INFO: 事务提交成功 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-04 09:17:09,383 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 09:17:09,385 INFO: 验证成功: 菜单已创建 ID=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-04 09:17:09,385 INFO: 周菜单创建成功: id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 09:17:09,386 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 39, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 09:19:36,729 INFO: 获取副表数据用于补全主表: weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-04 09:19:36,738 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-04 09:19:36,738 INFO: 主表数据补全完成，准备保存: 总菜品数=14, 已补全=14, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-04 09:19:36,741 INFO: 删除现有菜单食谱(主表): weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-04 09:19:36,744 INFO: 删除现有菜单食谱(副表): weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-04 09:19:36,770 INFO: 保存周菜单成功(主表和副表): id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-04 09:19:36,771 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 09:25:00,490 INFO: 获取副表数据用于补全主表: weekly_menu_id=39 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-04 09:25:00,494 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 花菜炒肉, ID=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,494 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 蒸蛋羹, ID=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,495 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,495 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 茄子烧豆角, ID=383 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,495 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 米饭, ID=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,495 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 三鲜菠菜, ID=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,496 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 豆腐汤, ID=404 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,496 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 芹菜炒肉, ID=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,496 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 剁椒笋片, ID=393 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,496 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 炒大白菜, ID=399 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,496 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 香干炒肉, ID=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,496 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 砂窝蒜苔, ID=384 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,497 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 小白菜炖豆腐, ID=392 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,497 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 酸辣土豆丝, ID=381 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-04 09:25:00,497 INFO: 副表数据映射构建完成: 1 天, 14 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:370]
