2025-06-04 09:06:12,029 INFO: 复制食谱 - 原食谱ID: 165, 名称: 芹菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-04 09:06:12,033 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-04 09:06:12,040 INFO: 用户区域信息 - user_area: 岳阳县荣湾湖小学, ID: 45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-04 09:06:22,776 INFO: 复制食谱 - 原食谱ID: 160, 名称: 香干炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-04 09:06:22,776 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-04 09:06:22,778 INFO: 用户区域信息 - user_area: 岳阳县荣湾湖小学, ID: 45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-04 09:06:27,959 INFO: 复制食谱 - 原食谱ID: 84, 名称: 猪肉汤 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-04 09:06:27,959 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-04 09:06:27,962 INFO: 用户区域信息 - user_area: 岳阳县荣湾湖小学, ID: 45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-04 09:06:43,913 INFO: 复制食谱 - 原食谱ID: 167, 名称: 花菜炒肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-04 09:06:43,913 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-04 09:06:43,915 INFO: 用户区域信息 - user_area: 岳阳县荣湾湖小学, ID: 45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-04 09:07:45,435 INFO: 收到创建周菜单请求: b'{"area_id":"45","week_start":"2025-06-02"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 09:07:45,436 INFO: 创建周菜单参数: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 09:07:45,438 INFO: 检查用户权限: user_id=37, area_id=45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 09:07:45,438 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 09:07:45,442 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 09:07:45,442 INFO: 开始创建周菜单: area_id=45, week_start=2025-06-02, created_by=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 09:07:45,442 INFO: 开始创建周菜单: area_id=45, week_start=2025-06-02, created_by=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:134]
2025-06-04 09:07:45,443 INFO: 转换后的日期对象: 2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:140]
2025-06-04 09:07:45,443 INFO: 计算的周结束日期: 2025-06-08 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:152]
2025-06-04 09:07:45,443 INFO: 检查是否已存在该周的菜单: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:155]
2025-06-04 09:07:45,444 INFO: 获取周菜单: area_id=45, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 09:07:45,444 INFO: 使用优化后的查询: area_id=45, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 09:07:45,446 ERROR: 优化查询失败，尝试备用方案: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start = ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:86]
2025-06-04 09:07:45,456 ERROR: 备用查询也失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start >= ? AND weekly_menus.week_start < ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0), datetime.datetime(2025, 6, 3, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:113]
2025-06-04 09:07:45,457 ERROR: 获取周菜单失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT TOP 1 weekly_menus.id AS weekly_menus_id, weekly_menus.area_id AS weekly_menus_area_id, weekly_menus.week_start AS weekly_menus_week_start, weekly_menus.week_end AS weekly_menus_week_end, weekly_menus.status AS weekly_menus_status, weekly_menus.created_by AS weekly_menus_created_by, weekly_menus.created_at AS weekly_menus_created_at, weekly_menus.updated_at AS weekly_menus_updated_at 
FROM weekly_menus 
WHERE weekly_menus.area_id = ? AND weekly_menus.week_start >= ? AND weekly_menus.week_start < ?]
[parameters: ('45', datetime.datetime(2025, 6, 2, 0, 0), datetime.datetime(2025, 6, 3, 0, 0))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:117]
