2025-06-04 17:32:10,444 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 17:32:10,460 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 17:32:10,460 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-02, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 17:32:10,460 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-02, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-04 17:32:10,460 INFO: 转换后的日期对象: 2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 17:32:10,460 INFO: 计算的周结束日期: 2025-06-08 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 17:32:10,460 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 17:32:10,460 INFO: 获取周菜单: area_id=44, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 17:32:10,460 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 17:32:10,460 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-04 17:32:10,460 INFO: 主查询未找到菜单: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-04 17:32:10,460 INFO: 使用日期字符串: week_start_str=2025-06-02, week_end_str=2025-06-08 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-04 17:32:10,460 INFO: 准备执行SQL创建菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-04 17:32:10,460 INFO: SQL参数: {'area_id': '44', 'week_start_str': '2025-06-02', 'week_end_str': '2025-06-08', 'status': '计划中', 'created_by': 36} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-04 17:32:10,460 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-04 17:32:10,460 INFO: SQL执行成功，获取到ID: 41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-04 17:32:10,460 INFO: 检查数据库连接状态 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-04 17:32:10,460 INFO: 数据库连接正常 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-04 17:32:10,460 INFO: 事务提交成功 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-04 17:32:10,460 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 17:32:10,476 INFO: 验证成功: 菜单已创建 ID=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-04 17:32:10,476 INFO: 周菜单创建成功: id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 17:32:10,476 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 41, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 17:32:17,377 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-02"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 17:32:17,377 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 17:32:17,383 INFO: 检查用户权限: user_id=36, area_id=44 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 17:32:17,383 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 17:32:17,383 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 17:32:17,383 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-02, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 17:32:17,383 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-02, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-04 17:32:17,383 INFO: 转换后的日期对象: 2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 17:32:17,383 INFO: 计算的周结束日期: 2025-06-08 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 17:32:17,383 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 17:32:17,383 INFO: 获取周菜单: area_id=44, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 17:32:17,383 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 17:32:17,383 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-04 17:32:17,383 INFO: 主查询成功，找到菜单: id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:97]
2025-06-04 17:32:17,383 INFO: 已存在该周的菜单: id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:208]
2025-06-04 17:32:17,383 INFO: 周菜单创建成功: id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 17:32:17,383 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 41, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 17:32:29,385 ERROR: 周菜单操作异常: time data 'undefined' does not match format '%Y-%m-%d' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py:115]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py", line 104, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 130, in plan
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data 'undefined' does not match format '%Y-%m-%d'
2025-06-04 17:33:15,214 INFO: 获取副表数据用于补全主表: weekly_menu_id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-04 17:33:15,214 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-04 17:33:15,214 INFO: 主表数据补全完成，准备保存: 总菜品数=4, 已补全=4, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-04 17:33:15,214 INFO: 删除现有菜单食谱(主表): weekly_menu_id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-04 17:33:15,214 INFO: 删除现有菜单食谱(副表): weekly_menu_id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:437]
