-- 检查当前索引状况和性能影响
PRINT '检查食品留样管理相关表的索引状况...'
PRINT '========================================'

-- 1. 检查 food_samples 表的所有索引
PRINT '1. food_samples 表的索引：'
SELECT 
    i.name AS index_name,
    i.type_desc AS index_type,
    i.is_unique,
    STUFF((
        SELECT ', ' + c.name + CASE WHEN ic.is_descending_key = 1 THEN ' DESC' ELSE ' ASC' END
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS key_columns,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 1
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS included_columns
FROM sys.indexes i
WHERE i.object_id = OBJECT_ID('food_samples')
ORDER BY i.name

PRINT ''
PRINT '2. menu_plans 表的索引：'
SELECT 
    i.name AS index_name,
    i.type_desc AS index_type,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS key_columns
FROM sys.indexes i
WHERE i.object_id = OBJECT_ID('menu_plans')
ORDER BY i.name

PRINT ''
PRINT '3. 检查索引使用统计（需要有查询活动后才有数据）：'
SELECT 
    OBJECT_NAME(s.object_id) AS table_name,
    i.name AS index_name,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan,
    s.last_user_lookup
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE s.database_id = DB_ID()
    AND OBJECT_NAME(s.object_id) IN ('food_samples', 'menu_plans', 'recipes', 'users', 'administrative_areas')
ORDER BY table_name, index_name

PRINT ''
PRINT '4. 检查可能的重复或冗余索引：';
-- 查找可能重复的索引
WITH IndexColumns AS (
    SELECT 
        i.object_id,
        i.index_id,
        i.name AS index_name,
        STUFF((
            SELECT ', ' + c.name
            FROM sys.index_columns ic2
            INNER JOIN sys.columns c ON ic2.object_id = c.object_id AND ic2.column_id = c.column_id
            WHERE ic2.object_id = i.object_id AND ic2.index_id = i.index_id AND ic2.is_included_column = 0
            ORDER BY ic2.key_ordinal
            FOR XML PATH('')
        ), 1, 2, '') AS key_columns
    FROM sys.indexes i
    WHERE i.object_id IN (OBJECT_ID('food_samples'), OBJECT_ID('menu_plans'))
)
SELECT 
    OBJECT_NAME(ic1.object_id) AS table_name,
    ic1.index_name AS index1,
    ic2.index_name AS index2,
    ic1.key_columns
FROM IndexColumns ic1
INNER JOIN IndexColumns ic2 ON ic1.object_id = ic2.object_id 
    AND ic1.key_columns = ic2.key_columns 
    AND ic1.index_id < ic2.index_id

PRINT ''
PRINT '========================================'
PRINT '索引检查完成！'
PRINT '如果发现问题，建议：'
PRINT '1. 删除重复或很少使用的索引'
PRINT '2. 合并相似的索引'
PRINT '3. 只保留最关键的索引'
PRINT '========================================'
