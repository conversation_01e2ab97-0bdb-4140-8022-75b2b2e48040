-- 修复的数据库文件状态检查
PRINT '检查数据库文件状态...'
PRINT '========================================'

SELECT 
    name AS file_name,
    type_desc AS file_type,
    CAST(size AS BIGINT) * 8 / 1024 AS size_mb,
    CASE 
        WHEN max_size = -1 THEN '无限制'
        WHEN max_size = 0 THEN '不增长'
        ELSE CAST(CAST(max_size AS BIGINT) * 8 / 1024 AS VARCHAR) + ' MB'
    END AS max_size_mb,
    growth AS growth_setting,
    is_percent_growth,
    physical_name
FROM sys.database_files

PRINT ''
PRINT '数据库文件检查完成！'
