-- 在更新单位之前备份当前的单位数据
-- 执行 update_all_units_to_kg.sql 之前请先执行此脚本

PRINT '=== 开始备份当前单位数据 ===';

-- 创建备份表来保存原始单位数据
BEGIN TRY

-- 1. 备份食材表的单位数据
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'ingredients_units_backup')
    DROP TABLE ingredients_units_backup;

SELECT 
    id,
    name,
    unit as original_unit,
    standard_unit as original_standard_unit,
    GETDATE() as backup_time
INTO ingredients_units_backup
FROM ingredients;

PRINT '✓ 已备份食材表单位数据到 ingredients_units_backup 表';

-- 2. 备份采购订单项表的单位数据
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_order_items_units_backup')
    DROP TABLE purchase_order_items_units_backup;

SELECT 
    id,
    order_id,
    ingredient_id,
    unit as original_unit,
    GETDATE() as backup_time
INTO purchase_order_items_units_backup
FROM purchase_order_items;

PRINT '✓ 已备份采购订单项表单位数据到 purchase_order_items_units_backup 表';

-- 3. 备份库存表的单位数据
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'inventories_units_backup')
    DROP TABLE inventories_units_backup;

SELECT 
    id,
    ingredient_id,
    unit as original_unit,
    GETDATE() as backup_time
INTO inventories_units_backup
FROM inventories;

PRINT '✓ 已备份库存表单位数据到 inventories_units_backup 表';

-- 4. 备份入库明细表的单位数据
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_in_items_units_backup')
    DROP TABLE stock_in_items_units_backup;

SELECT 
    id,
    stock_in_id,
    ingredient_id,
    unit as original_unit,
    GETDATE() as backup_time
INTO stock_in_items_units_backup
FROM stock_in_items;

PRINT '✓ 已备份入库明细表单位数据到 stock_in_items_units_backup 表';

-- 5. 备份出库明细表的单位数据（如果存在）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items')
BEGIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items_units_backup')
        DROP TABLE stock_out_items_units_backup;

    SELECT 
        id,
        stock_out_id,
        ingredient_id,
        unit as original_unit,
        GETDATE() as backup_time
    INTO stock_out_items_units_backup
    FROM stock_out_items;

    PRINT '✓ 已备份出库明细表单位数据到 stock_out_items_units_backup 表';
END

-- 6. 备份消耗明细表的单位数据
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_details_units_backup')
    DROP TABLE consumption_details_units_backup;

SELECT 
    id,
    consumption_plan_id,
    ingredient_id,
    unit as original_unit,
    GETDATE() as backup_time
INTO consumption_details_units_backup
FROM consumption_details;

PRINT '✓ 已备份消耗明细表单位数据到 consumption_details_units_backup 表';

-- 7. 备份食谱食材表的单位数据
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'recipe_ingredients_units_backup')
    DROP TABLE recipe_ingredients_units_backup;

SELECT 
    recipe_id,
    ingredient_id,
    unit as original_unit,
    GETDATE() as backup_time
INTO recipe_ingredients_units_backup
FROM recipe_ingredients;

PRINT '✓ 已备份食谱食材表单位数据到 recipe_ingredients_units_backup 表';

PRINT '';
PRINT '✅ 所有单位数据备份完成！';

-- 显示备份统计
PRINT '';
PRINT '📊 备份数据统计：';

SELECT '食材表' as 表名, COUNT(*) as 备份记录数 FROM ingredients_units_backup
UNION ALL
SELECT '采购订单项表' as 表名, COUNT(*) as 备份记录数 FROM purchase_order_items_units_backup
UNION ALL
SELECT '库存表' as 表名, COUNT(*) as 备份记录数 FROM inventories_units_backup
UNION ALL
SELECT '入库明细表' as 表名, COUNT(*) as 备份记录数 FROM stock_in_items_units_backup
UNION ALL
SELECT '消耗明细表' as 表名, COUNT(*) as 备份记录数 FROM consumption_details_units_backup
UNION ALL
SELECT '食谱食材表' as 表名, COUNT(*) as 备份记录数 FROM recipe_ingredients_units_backup;

-- 如果出库明细表存在，也显示其统计
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items_units_backup')
BEGIN
    SELECT '出库明细表' as 表名, COUNT(*) as 备份记录数 FROM stock_out_items_units_backup;
END

END TRY
BEGIN CATCH
    PRINT '❌ 备份过程中出现错误: ' + ERROR_MESSAGE();
    THROW;
END CATCH

PRINT '';
PRINT '📋 备份完成，现在可以安全执行 update_all_units_to_kg.sql 脚本';
PRINT '💡 如需回滚，请执行 restore_units_from_backup.sql 脚本';
