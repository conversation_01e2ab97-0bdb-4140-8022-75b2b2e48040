{% extends "base.html" %}

{% block title %}视频资源管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-video mr-2"></i>用户引导视频资源管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#uploadVideoModal">
                            <i class="fas fa-upload mr-1"></i>上传视频
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for step_name, step_videos in video_resources.items() %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">{{ step_videos.title }}</h6>
                                    <small>总时长: {{ step_videos.duration }}</small>
                                </div>
                                <div class="card-body">
                                    {% if step_videos.videos %}
                                        {% for video in step_videos.videos %}
                                        <div class="video-item mb-3 p-2 border rounded">
                                            <div class="row">
                                                <div class="col-4">
                                                    <img src="{{ video.thumbnail }}" class="img-fluid rounded" alt="视频缩略图"
                                                         onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y4ZjlmYSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNmM3NTdkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6KeG6aKRPC90ZXh0Pjwvc3ZnPg=='">
                                                </div>
                                                <div class="col-8">
                                                    <h6 class="mb-1">{{ video.name }}</h6>
                                                    <p class="small text-muted mb-1">{{ video.duration }}</p>
                                                    <p class="small mb-2">{{ video.description }}</p>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" data-onclick="previewVideo('{{ video.url }}', '{{ video.name }}')">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                        <button class="btn btn-outline-warning" onclick="editVideo('{{ step_name }}', '{{ video.name }}')">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" data-onclick="deleteVideo('{{ step_name }}', '{{ video.name }}')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="text-center text-muted">
                                            <i class="fas fa-video-slash fa-2x mb-2"></i>
                                            <p>暂无视频资源</p>
                                            <button class="btn btn-sm btn-outline-primary" data-onclick="uploadVideoForStep('{{ step_name }}')" data-confirm-message="确定要删除吗？" style="cursor: pointer;">
                                                <i class="fas fa-plus mr-1"></i>添加视频
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传视频模态框 -->
<div class="modal fade" id="uploadVideoModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload mr-2"></i>上传引导视频
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="uploadVideoForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="stepSelect">引导步骤</label>
                        <select class="form-control" id="stepSelect" name="step_name" required>
                            <option value="">请选择引导步骤</option>
                            <option value="daily_management">日常管理模块</option>
                            <option value="suppliers">供应商管理</option>
                            <option value="ingredients_recipes">食材食谱管理</option>
                            <option value="weekly_menu">周菜单制定</option>
                            <option value="purchase_order">采购订单管理</option>
                            <option value="stock_in">食材入库管理</option>
                            <option value="consumption_plan">消耗量计划</option>
                            <option value="stock_out">食材出库管理</option>
                            <option value="traceability">食材溯源管理</option>
                            <option value="food_samples">留样记录管理</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="videoName">视频名称</label>
                        <input type="text" class="form-control" id="videoName" name="video_name" required>
                    </div>

                    <div class="form-group">
                        <label for="videoDescription">视频描述</label>
                        <textarea class="form-control" id="videoDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="videoFile">视频文件</label>
                        <input type="file" class="form-control-file" id="videoFile" name="video" accept="video/*" required>
                        <small class="form-text text-muted">支持MP4、AVI、MOV格式，建议文件大小不超过100MB</small>
                    </div>

                    <div class="form-group">
                        <label for="thumbnailFile">缩略图</label>
                        <input type="file" class="form-control-file" id="thumbnailFile" name="thumbnail_file" accept="image/*">
                        <small class="form-text text-muted">可选，支持JPG、PNG格式</small>
                    </div>
                </form>

                <div id="uploadProgress" class="progress mt-3" style="display: none;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" data-onclick="uploadVideo()">
                    <i class="fas fa-upload mr-1"></i>上传视频
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑视频模态框 -->
<div class="modal fade" id="editVideoModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit mr-2"></i>编辑视频信息
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editVideoForm">
                    <div class="form-group">
                        <label for="editStepName">引导步骤</label>
                        <input type="text" class="form-control" id="editStepName" readonly>
                        <small class="form-text text-muted">引导步骤不可修改</small>
                    </div>

                    <div class="form-group">
                        <label for="editVideoName">视频名称</label>
                        <input type="text" class="form-control" id="editVideoName" name="video_name" required>
                    </div>

                    <div class="form-group">
                        <label for="editVideoDescription">视频描述</label>
                        <textarea class="form-control" id="editVideoDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="editVideoFile">更换视频文件（可选）</label>
                        <input type="file" class="form-control-file" id="editVideoFile" name="video" accept="video/*">
                        <small class="form-text text-muted">如不选择文件则保持原视频不变</small>
                    </div>

                    <!-- 更新进度条 -->
                    <div id="updateProgress" class="progress mb-3" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%">
                            <span class="progress-text">准备更新...</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="updateVideoBtn" data-onclick="updateVideo()">
                    <i class="fas fa-save mr-1"></i>保存修改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 视频预览模态框 -->
<div class="modal fade" id="videoPreviewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewVideoTitle">视频预览</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <video id="previewVideo" controls width="100%" height="400" style="display: none;">
                    您的浏览器不支持视频播放。
                </video>
                <div id="videoLoadError" class="text-center p-4" style="display: none;">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>视频加载失败</h5>
                    <p class="text-muted">视频文件可能不存在或格式不支持</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
window.previewVideo = function previewVideo(videoUrl, videoName) {
    $('#previewVideoTitle').text(videoName);
    const video = $('#previewVideo')[0];

    video.src = videoUrl;
    video.style.display = 'block';
    $('#videoLoadError').hide();

    video.onerror = function() {
        video.style.display = 'none';
        $('#videoLoadError').show();
    };

    $('#videoPreviewModal').modal('show');
}

window.uploadVideoForStep = function uploadVideoForStep(stepName) {
    $('#stepSelect').val(stepName);
    $('#uploadVideoModal').modal('show');
}

window.uploadVideo = function uploadVideo() {
    const form = $('#uploadVideoForm')[0];
    const formData = new FormData(form);

    // 显示进度条
    $('#uploadProgress').show();

    $.ajax({
        url: '/admin/guide/videos/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener("progress", function(evt) {
                if (evt.lengthComputable) {
                    const percentComplete = evt.loaded / evt.total * 100;
                    $('.progress-bar').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function(data) {
            if (data.success) {
                alert('视频上传成功！');
                $('#uploadVideoModal').modal('hide');
                location.reload();
            } else {
                alert('上传失败：' + data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('上传失败:', error);
            alert('上传失败，请稍后重试');
        },
        complete: function() {
            $('#uploadProgress').hide();
            $('.progress-bar').css('width', '0%');
        }
    });
}

// 简化的编辑视频函数
function editVideo(stepName, videoName) {
    console.log('编辑视频 - 步骤:', stepName, '视频:', videoName);

    // 重置表单
    resetEditForm();

    // 步骤名称映射
    const stepNames = {
        'daily_management': '食堂日常管理模块',
        'suppliers': '供应商管理',
        'ingredients_recipes': '食材食谱管理',
        'weekly_menu': '周菜单计划',
        'purchase_order': '采购订单',
        'stock_in': '食材入库',
        'consumption_plan': '消耗量计划',
        'stock_out': '食材出库',
        'traceability': '食材溯源',
        'food_samples': '留样记录'
    };

    // 直接设置字段值
    $('#editStepName').val(stepNames[stepName] || stepName);
    $('#editVideoName').val(videoName);
    $('#editVideoDescription').val('');

    // 保存原始数据
    if (!$('#editStepNameHidden').length) {
        $('#editVideoForm').append('<input type="hidden" id="editStepNameHidden" name="step_name">');
    }
    if (!$('#editOriginalVideoNameHidden').length) {
        $('#editVideoForm').append('<input type="hidden" id="editOriginalVideoNameHidden" name="original_video_name">');
    }
    $('#editStepNameHidden').val(stepName);
    $('#editOriginalVideoNameHidden').val(videoName);

    // 显示对话框
    $('#editVideoModal').modal('show');
}



function getStepDisplayName(stepName) {
    const stepNames = {
        'welcome': '欢迎使用校园餐智慧食堂平台',
        'daily_management': '食堂日常管理模块',
        'suppliers': '供应商管理',
        'ingredients_recipes': '食材食谱管理',
        'weekly_menu': '周菜单计划',
        'purchase_order': '采购订单',
        'stock_in': '食材入库',
        'consumption_plan': '消耗量计划',
        'stock_out': '食材出库',
        'traceability': '食材溯源',
        'food_samples': '留样记录',
        'completed': '引导完成'
    };
    return stepNames[stepName] || stepName;
}

function resetEditForm() {
    // 重置进度条
    $('#updateProgress').hide();
    $('#updateProgress .progress-bar').css('width', '0%');
    $('#updateProgress .progress-bar').removeClass('bg-success');
    $('#updateProgress .progress-bar').addClass('progress-bar-striped progress-bar-animated');
    $('#updateProgress .progress-text').text('准备更新...');

    // 重置按钮状态
    $('#updateVideoBtn').prop('disabled', false);

    // 清空文件选择
    $('#editVideoFile').val('');
}

window.updateVideo = function updateVideo() {
    const form = $('#editVideoForm')[0];
    const formData = new FormData(form);

    // 添加原始视频名称用于识别要更新的视频
    const originalVideoName = $('#editOriginalVideoNameHidden').val();
    formData.append('original_video_name', originalVideoName);

    // 调试信息
    console.log('更新视频参数:', {
        step_name: $('#editStepNameHidden').val(),
        video_name: $('#editVideoName').val(),
        original_video_name: originalVideoName,
        description: $('#editVideoDescription').val()
    });

    // 显示进度条
    $('#updateProgress').show();
    $('#updateVideoBtn').prop('disabled', true);

    // 检查是否有文件上传
    const fileInput = $('#editVideoFile')[0];
    const hasFile = fileInput.files.length > 0;

    if (hasFile) {
        // 有文件上传，显示上传进度
        updateProgressBar(20, '准备上传文件...');
    } else {
        // 只更新信息，显示处理进度
        updateProgressBar(50, '更新视频信息...');
    }

    $.ajax({
        url: '/admin/guide/videos/update',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            if (hasFile) {
                // 只有在上传文件时才监听进度
                xhr.upload.addEventListener('progress', function(evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        updateProgressBar(percentComplete, `上传中... ${Math.round(percentComplete)}%`);
                    }
                }, false);
            }
            return xhr;
        },
        success: function(data) {
            if (data.success) {
                updateProgressBar(100, '更新完成！');
                setTimeout(() => {
                    alert('视频信息更新成功！');
                    $('#editVideoModal').modal('hide');
                    location.reload();
                }, 500);
            } else {
                updateProgressBar(0, '更新失败');
                $('#updateVideoBtn').prop('disabled', false);
                $('#updateProgress').hide();
                alert('更新失败：' + data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('更新失败:', error);
            updateProgressBar(0, '更新失败');
            $('#updateVideoBtn').prop('disabled', false);
            $('#updateProgress').hide();
            alert('更新失败: ' + (xhr.responseJSON?.message || error));
        }
    });
}

function updateProgressBar(percent, text) {
    const progressBar = $('#updateProgress .progress-bar');
    const progressText = $('#updateProgress .progress-text');

    progressBar.css('width', percent + '%');
    progressText.text(text);

    if (percent >= 100) {
        progressBar.removeClass('progress-bar-striped progress-bar-animated');
        progressBar.addClass('bg-success');
    }
}

window.deleteVideo = function deleteVideo(stepName, videoName) {
    if (confirm('确定要删除这个视频吗？此操作不可恢复。')) {
        $.ajax({
            url: '/admin/guide/videos/delete',
            type: 'POST',
            data: {
                step_name: stepName,
                video_name: videoName
            },
            success: function(data) {
                if (data.success) {
                    alert('视频删除成功！');
                    location.reload();
                } else {
                    alert('删除失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('删除失败:', error);
                alert('删除失败，请稍后重试');
            }
        });
    }
}
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>