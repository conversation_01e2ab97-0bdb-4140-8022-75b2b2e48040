-- 将食材库表中的食材单位改成公斤
-- 执行前请先备份数据库

PRINT '=== 开始更新食材单位为公斤 ===';

-- 1. 首先查看当前食材单位分布情况
PRINT '1. 当前食材单位分布情况：';
SELECT 
    unit as 当前单位,
    COUNT(*) as 数量,
    CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ingredients) AS DECIMAL(5,2)) as 百分比
FROM ingredients 
WHERE status = 1  -- 只统计启用的食材
GROUP BY unit
ORDER BY COUNT(*) DESC;

PRINT '';

-- 2. 查看当前标准单位分布情况
PRINT '2. 当前标准单位分布情况：';
SELECT 
    standard_unit as 当前标准单位,
    COUNT(*) as 数量,
    CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ingredients WHERE standard_unit IS NOT NULL) AS DECIMAL(5,2)) as 百分比
FROM ingredients 
WHERE status = 1 AND standard_unit IS NOT NULL
GROUP BY standard_unit
ORDER BY COUNT(*) DESC;

PRINT '';

-- 3. 开始更新操作
PRINT '3. 开始更新食材单位...';

BEGIN TRANSACTION;

BEGIN TRY
    -- 更新所有食材的 unit 字段为 '公斤'
    UPDATE ingredients 
    SET unit = '公斤',
        updated_at = GETDATE()
    WHERE status = 1;  -- 只更新启用的食材
    
    DECLARE @updated_unit_count INT = @@ROWCOUNT;
    PRINT '✓ 已更新 ' + CAST(@updated_unit_count AS VARCHAR(10)) + ' 个食材的单位为公斤';
    
    -- 更新所有食材的 standard_unit 字段为 '公斤'
    UPDATE ingredients 
    SET standard_unit = '公斤',
        updated_at = GETDATE()
    WHERE status = 1;
    
    DECLARE @updated_standard_unit_count INT = @@ROWCOUNT;
    PRINT '✓ 已更新 ' + CAST(@updated_standard_unit_count AS VARCHAR(10)) + ' 个食材的标准单位为公斤';
    
    -- 提交事务
    COMMIT TRANSACTION;
    PRINT '✓ 事务提交成功';
    
END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION;
    PRINT '❌ 更新失败，事务已回滚: ' + ERROR_MESSAGE();
    THROW;
END CATCH

PRINT '';

-- 4. 验证更新结果
PRINT '4. 验证更新结果：';
SELECT 
    '更新后统计' as 类型,
    COUNT(*) as 总食材数,
    SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 单位为公斤的数量,
    SUM(CASE WHEN standard_unit = '公斤' THEN 1 ELSE 0 END) as 标准单位为公斤的数量,
    SUM(CASE WHEN unit = '公斤' AND standard_unit = '公斤' THEN 1 ELSE 0 END) as 两个字段都为公斤的数量
FROM ingredients 
WHERE status = 1;

PRINT '';

-- 5. 显示更新后的单位分布
PRINT '5. 更新后的单位分布：';
SELECT 
    unit as 单位,
    standard_unit as 标准单位,
    COUNT(*) as 数量
FROM ingredients 
WHERE status = 1
GROUP BY unit, standard_unit
ORDER BY COUNT(*) DESC;

PRINT '';
PRINT '=== 食材单位更新完成 ===';

-- 6. 重要提醒
PRINT '';
PRINT '⚠️  重要提醒：';
PRINT '1. 请检查相关的采购订单、库存记录、消耗计划等表中的单位字段';
PRINT '2. 可能需要同步更新以下表的单位字段：';
PRINT '   - purchase_order_items (采购订单项)';
PRINT '   - inventories (库存)';
PRINT '   - stock_in_items (入库明细)';
PRINT '   - stock_out_items (出库明细)';
PRINT '   - consumption_details (消耗明细)';
PRINT '   - recipe_ingredients (食谱食材)';
PRINT '3. 建议在业务低峰期执行，并通知相关用户';
PRINT '4. 执行后请测试相关功能是否正常';
