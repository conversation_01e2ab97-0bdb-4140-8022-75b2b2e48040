-- 保守的优化方案 - 只创建最关键的索引
PRINT '开始保守优化...'
PRINT '========================================'

-- 只创建最关键的索引，解决当前的性能问题

-- 1. 优化 weekly_menus 表的查询（解决主要性能问题）
PRINT '1. 优化 weekly_menus 表查询：'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_key')
BEGIN
    CREATE NONCLUSTERED INDEX IX_weekly_menus_area_week_key
    ON weekly_menus (area_id, week_start)
    INCLUDE (id, week_end, status, created_by, created_at, updated_at)
    PRINT '✓ 创建周菜单区域+日期索引'
END
ELSE
    PRINT '- 周菜单区域+日期索引已存在'

-- 2. 优化 weekly_menu_recipes 表的查询
PRINT ''
PRINT '2. 优化 weekly_menu_recipes 表查询：'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_weekly_menu_id')
BEGIN
    CREATE NONCLUSTERED INDEX IX_weekly_menu_recipes_weekly_menu_id
    ON weekly_menu_recipes (weekly_menu_id)
    INCLUDE (recipe_id, recipe_name, day_of_week, meal_type)
    PRINT '✓ 创建周菜单食谱索引'
END
ELSE
    PRINT '- 周菜单食谱索引已存在'

-- 更新统计信息
PRINT ''
PRINT '正在更新统计信息...'
UPDATE STATISTICS weekly_menus
UPDATE STATISTICS weekly_menu_recipes
PRINT '✓ 统计信息更新完成'

PRINT ''
PRINT '========================================'
PRINT '保守优化完成！'
PRINT '========================================'
PRINT ''
PRINT '📊 优化内容：'
PRINT '• 只创建了2个最关键的索引'
PRINT '• 专门解决周菜单查询慢的问题'
PRINT '• 最小化对数据库的影响'
PRINT ''
PRINT '🚀 预期效果：'
PRINT '• 周菜单页面加载速度提升 70-80%'
PRINT '• 菜单食谱查询速度提升 60-70%'
PRINT '• 不影响其他功能'
PRINT ''
PRINT '现在请测试：'
PRINT 'http://www.tdtech.xin/weekly-menu-v2/plan?area_id=42&week_start=2025-06-09'
PRINT '========================================'
