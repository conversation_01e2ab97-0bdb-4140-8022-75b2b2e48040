2025-06-04 10:28:37,168 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-04 10:28:37,173 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-04 10:28:37,173 INFO: 星期几: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-04 10:28:37,178 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-04 10:28:37,183 INFO: 周菜单 39 中找到 4 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-04 10:28:37,188 INFO: 分析食谱: 🏫 米饭, recipe_id=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:37,192 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:37,200 INFO: 分析食谱: 🏫 香干炒肉, recipe_id=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:37,201 INFO: 找到 2 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:37,204 INFO: 分析食谱: 🏫 清炒生菜, recipe_id=400 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:37,205 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:37,207 INFO: 分析食谱: 🏫 花菜炒肉, recipe_id=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:37,208 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,288 INFO: 收到食谱分析请求: {'area_id': 45, 'consumption_date': '2025-06-04', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:338]
2025-06-04 10:28:54,290 INFO: 查询日期: 2025-06-04, 区域: 45, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:356]
2025-06-04 10:28:54,290 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-04 10:28:54,293 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-04 10:28:54,294 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-04 10:28:54,294 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-04 10:28:54,294 INFO: 星期几: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-04 10:28:54,296 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-04 10:28:54,299 INFO: 周菜单 39 中找到 4 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-04 10:28:54,301 INFO: 分析食谱: 🏫 米饭, recipe_id=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,302 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,308 INFO: 分析食谱: 🏫 香干炒肉, recipe_id=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,309 INFO: 找到 2 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,313 INFO: 分析食谱: 🏫 清炒生菜, recipe_id=400 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,314 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,316 INFO: 分析食谱: 🏫 花菜炒肉, recipe_id=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,316 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,320 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-04 10:28:54,321 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-04 10:28:54,322 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-04 10:28:54,322 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-04 10:28:54,322 INFO: 星期几: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-04 10:28:54,323 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-04 10:28:54,324 INFO: 周菜单 39 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-04 10:28:54,325 INFO: 分析食谱: 🏫 三鲜菠菜, recipe_id=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,326 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,335 INFO: 分析食谱: 🏫 西红柿炒蛋, recipe_id=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,336 INFO: 找到 2 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,339 INFO: 分析食谱: 🏫 米饭, recipe_id=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,340 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,343 INFO: 分析食谱: 🏫 花菜炒肉, recipe_id=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,343 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,348 INFO: 分析食谱: 🏫 芹菜炒肉, recipe_id=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,349 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:28:54,354 INFO: 分析食谱: 🏫 猪肉汤, recipe_id=411 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 10:28:54,355 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 10:29:33,639 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 10:29:33,673 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 10:29:42,277 INFO: 用户 rwhxx2333 访问入库单创建页面，可访问区域IDs: [45] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:309]
2025-06-04 10:29:42,278 INFO: 找到 1 个可用的采购订单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:310]
2025-06-04 10:31:11,879 WARNING: Suspicious path blocked: /.env from 196.251.73.140 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 10:31:11,880 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 10:34:13,602 WARNING: Suspicious path blocked: /.env_sample from 198.55.98.76 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 10:34:13,603 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
