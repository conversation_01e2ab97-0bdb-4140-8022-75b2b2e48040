-- 基于检查结果的针对性索引优化
PRINT '开始针对性索引优化...'
PRINT '========================================'

-- 根据检查结果，我们发现的问题：
-- 1. IX_food_samples_data_safe (meal_date) 与 IX_food_samples_area_date_safe (area_id, meal_date) 重复
-- 2. food_samples 表有6个索引，略多

PRINT '分析结果：'
PRINT '• food_samples 表有 6 个索引（略多）'
PRINT '• 发现重复索引：IX_food_samples_data_safe'
PRINT '• 其他索引都在正常使用'
PRINT ''

-- 删除重复的索引
PRINT '1. 删除重复的索引：'

-- 删除 IX_food_samples_data_safe，因为它与 IX_food_samples_area_date_safe 功能重复
-- area_id + meal_date 的复合索引可以覆盖单独的 meal_date 查询
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_data_safe')
BEGIN
    DROP INDEX IX_food_samples_data_safe ON food_samples
    PRINT '✓ 删除重复索引：IX_food_samples_data_safe'
    PRINT '  原因：与 IX_food_samples_area_date_safe 功能重复'
END
ELSE
    PRINT '- IX_food_samples_data_safe 索引不存在或已删除'

PRINT ''
PRINT '2. 保留的关键索引：'
PRINT '✓ IX_food_samples_area_date_safe (area_id, meal_date) - 最重要的查询'
PRINT '✓ IX_food_samples_created_at_safe (created_at DESC) - 分页排序'
PRINT '✓ IX_food_samples_menu_plan_safe (menu_plan_id) - 菜单计划查询'
PRINT '✓ IX_food_samples_operator_date_safe (operator_id, meal_date) - 操作员查询'
PRINT '✓ PK_food_sam__321383FAD4052AU (主键) - 系统必需'

PRINT ''
PRINT '3. 检查是否需要优化现有索引：'

-- 检查 IX_food_samples_area_date_safe 的 INCLUDE 列是否合理
PRINT '检查主要索引的 INCLUDE 列设计...'

-- 如果发现 INCLUDE 列过多，可能需要优化
-- 但根据检查结果，当前索引都在正常使用，暂不修改

PRINT ''
PRINT '========================================'
PRINT '优化完成！'
PRINT '========================================'
PRINT ''
PRINT '📊 优化结果：'
PRINT '• 删除了 1 个重复索引'
PRINT '• food_samples 表现在有 5 个索引（合理）'
PRINT '• 保留了所有关键查询的索引'
PRINT ''
PRINT '🎯 当前索引策略：'
PRINT '1. area_id + meal_date - 覆盖最常用的区域日期查询'
PRINT '2. created_at DESC - 支持分页排序'
PRINT '3. menu_plan_id - 支持菜单计划关联查询'
PRINT '4. operator_id + meal_date - 支持操作员查询'
PRINT '5. 主键索引 - 系统必需'
PRINT ''
PRINT '🚀 预期效果：'
PRINT '• 减少索引维护开销'
PRINT '• 保持查询性能'
PRINT '• 简化索引结构'
PRINT '• 提高写入性能'

-- 更新统计信息
PRINT ''
PRINT '正在更新统计信息...'
UPDATE STATISTICS food_samples
UPDATE STATISTICS menu_plans
UPDATE STATISTICS recipes
UPDATE STATISTICS users
UPDATE STATISTICS administrative_areas
PRINT '✓ 统计信息更新完成'

PRINT ''
PRINT '========================================'
PRINT '🎉 针对性优化完成！'
PRINT ''
PRINT '建议测试以下功能的性能：'
PRINT '1. 食品留样管理页面加载'
PRINT '2. 按日期筛选留样记录'
PRINT '3. 按操作员筛选'
PRINT '4. 分页浏览'
PRINT '5. 菜单计划关联查询'
PRINT ''
PRINT '如果性能仍有问题，可能需要：'
PRINT '• 检查具体的查询语句'
PRINT '• 分析查询执行计划'
PRINT '• 考虑应用层面的优化'
PRINT '========================================'
