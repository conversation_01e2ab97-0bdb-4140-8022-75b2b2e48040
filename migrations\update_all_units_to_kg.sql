-- 将所有相关表中的食材单位统一改成公斤
-- 执行前请先备份数据库
-- 建议在业务低峰期执行

PRINT '=== 开始统一更新所有表的食材单位为公斤 ===';

-- 检查当前各表的单位分布情况
PRINT '1. 检查当前各表的单位分布情况：';

-- 食材表
PRINT '食材表 (ingredients):';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM ingredients 
WHERE status = 1
GROUP BY unit
ORDER BY COUNT(*) DESC;

-- 采购订单项表
PRINT '采购订单项表 (purchase_order_items):';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM purchase_order_items
GROUP BY unit
ORDER BY COUNT(*) DESC;

-- 库存表
PRINT '库存表 (inventories):';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM inventories
GROUP BY unit
ORDER BY COUNT(*) DESC;

-- 入库明细表
PRINT '入库明细表 (stock_in_items):';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM stock_in_items
GROUP BY unit
ORDER BY COUNT(*) DESC;

-- 出库明细表（如果存在）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items')
BEGIN
    PRINT '出库明细表 (stock_out_items):';
    SELECT 
        unit as 单位,
        COUNT(*) as 数量
    FROM stock_out_items
    GROUP BY unit
    ORDER BY COUNT(*) DESC;
END

-- 消耗明细表
PRINT '消耗明细表 (consumption_details):';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM consumption_details
GROUP BY unit
ORDER BY COUNT(*) DESC;

-- 食谱食材表
PRINT '食谱食材表 (recipe_ingredients):';
SELECT 
    unit as 单位,
    COUNT(*) as 数量
FROM recipe_ingredients
GROUP BY unit
ORDER BY COUNT(*) DESC;

PRINT '';
PRINT '2. 开始批量更新操作...';

BEGIN TRANSACTION;

BEGIN TRY
    DECLARE @total_updated INT = 0;
    DECLARE @table_updated INT;
    
    -- 更新食材表
    UPDATE ingredients 
    SET unit = '公斤',
        standard_unit = '公斤',
        updated_at = GETDATE()
    WHERE status = 1;
    
    SET @table_updated = @@ROWCOUNT;
    SET @total_updated = @total_updated + @table_updated;
    PRINT '✓ 食材表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    
    -- 更新采购订单项表
    UPDATE purchase_order_items 
    SET unit = '公斤',
        updated_at = GETDATE();
    
    SET @table_updated = @@ROWCOUNT;
    SET @total_updated = @total_updated + @table_updated;
    PRINT '✓ 采购订单项表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    
    -- 更新库存表
    UPDATE inventories 
    SET unit = '公斤';
    
    SET @table_updated = @@ROWCOUNT;
    SET @total_updated = @total_updated + @table_updated;
    PRINT '✓ 库存表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    
    -- 更新入库明细表
    UPDATE stock_in_items 
    SET unit = '公斤',
        updated_at = GETDATE();
    
    SET @table_updated = @@ROWCOUNT;
    SET @total_updated = @total_updated + @table_updated;
    PRINT '✓ 入库明细表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    
    -- 更新出库明细表（如果存在）
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_out_items')
    BEGIN
        UPDATE stock_out_items 
        SET unit = '公斤',
            updated_at = GETDATE();
        
        SET @table_updated = @@ROWCOUNT;
        SET @total_updated = @total_updated + @table_updated;
        PRINT '✓ 出库明细表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    END
    
    -- 更新消耗明细表
    UPDATE consumption_details 
    SET unit = '公斤',
        updated_at = GETDATE();
    
    SET @table_updated = @@ROWCOUNT;
    SET @total_updated = @total_updated + @table_updated;
    PRINT '✓ 消耗明细表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    
    -- 更新食谱食材表
    UPDATE recipe_ingredients 
    SET unit = '公斤';
    
    SET @table_updated = @@ROWCOUNT;
    SET @total_updated = @total_updated + @table_updated;
    PRINT '✓ 食谱食材表: 已更新 ' + CAST(@table_updated AS VARCHAR(10)) + ' 条记录';
    
    -- 提交事务
    COMMIT TRANSACTION;
    PRINT '';
    PRINT '✅ 所有更新操作完成！总共更新了 ' + CAST(@total_updated AS VARCHAR(10)) + ' 条记录';
    PRINT '✅ 事务提交成功';
    
END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION;
    PRINT '';
    PRINT '❌ 更新失败，所有操作已回滚: ' + ERROR_MESSAGE();
    THROW;
END CATCH

PRINT '';
PRINT '3. 验证更新结果：';

-- 验证各表的更新结果
SELECT '食材表' as 表名, 
       COUNT(*) as 总记录数,
       SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 公斤单位数量,
       SUM(CASE WHEN standard_unit = '公斤' THEN 1 ELSE 0 END) as 公斤标准单位数量
FROM ingredients WHERE status = 1

UNION ALL

SELECT '采购订单项表' as 表名,
       COUNT(*) as 总记录数,
       SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 公斤单位数量,
       0 as 公斤标准单位数量
FROM purchase_order_items

UNION ALL

SELECT '库存表' as 表名,
       COUNT(*) as 总记录数,
       SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 公斤单位数量,
       0 as 公斤标准单位数量
FROM inventories

UNION ALL

SELECT '入库明细表' as 表名,
       COUNT(*) as 总记录数,
       SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 公斤单位数量,
       0 as 公斤标准单位数量
FROM stock_in_items

UNION ALL

SELECT '消耗明细表' as 表名,
       COUNT(*) as 总记录数,
       SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 公斤单位数量,
       0 as 公斤标准单位数量
FROM consumption_details

UNION ALL

SELECT '食谱食材表' as 表名,
       COUNT(*) as 总记录数,
       SUM(CASE WHEN unit = '公斤' THEN 1 ELSE 0 END) as 公斤单位数量,
       0 as 公斤标准单位数量
FROM recipe_ingredients;

PRINT '';
PRINT '=== 统一单位更新完成 ===';
PRINT '';
PRINT '📋 后续建议：';
PRINT '1. 测试采购订单创建和编辑功能';
PRINT '2. 测试库存管理功能';
PRINT '3. 测试入库出库功能';
PRINT '4. 测试消耗计划功能';
PRINT '5. 测试食谱管理功能';
PRINT '6. 检查前端页面显示是否正确';
PRINT '7. 通知用户单位已统一为公斤';
