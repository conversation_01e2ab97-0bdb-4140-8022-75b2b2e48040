-- 清理过多的索引，只保留最关键的
PRINT '开始清理过多的索引...'
PRINT '========================================'

-- 删除可能重复或不必要的索引
-- 注意：只删除我们刚才创建的索引，不删除系统原有的

-- 1. 删除食品留样的部分索引，只保留最关键的
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_date_safe')
BEGIN
    DROP INDEX IX_food_samples_date_safe ON food_samples
    PRINT '✓ 删除食品留样日期索引（保留更全面的区域日期索引）'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_comprehensive_safe')
BEGIN
    DROP INDEX IX_food_samples_comprehensive_safe ON food_samples
    PRINT '✓ 删除食品留样综合索引（可能与其他索引重复）'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_comprehensive_fixed')
BEGIN
    DROP INDEX IX_food_samples_comprehensive_fixed ON food_samples
    PRINT '✓ 删除食品留样综合索引（修复版）'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_comprehensive_final')
BEGIN
    DROP INDEX IX_food_samples_comprehensive_final ON food_samples
    PRINT '✓ 删除食品留样综合索引（最终版）'
END

-- 2. 删除可能重复的用户表索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('users') AND name = 'IX_users_id_name_safe')
BEGIN
    DROP INDEX IX_users_id_name_safe ON users
    PRINT '✓ 删除用户表重复索引'
END

-- 3. 删除可能重复的区域表索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('administrative_areas') AND name = 'IX_administrative_areas_id_name_safe')
BEGIN
    DROP INDEX IX_administrative_areas_id_name_safe ON administrative_areas
    PRINT '✓ 删除区域表重复索引'
END

-- 4. 删除可能重复的菜谱表索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('recipes') AND name = 'IX_recipes_id_name_safe')
BEGIN
    DROP INDEX IX_recipes_id_name_safe ON recipes
    PRINT '✓ 删除菜谱表重复索引'
END

-- 5. 删除可能重复的菜单计划索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('menu_plans') AND name = 'IX_menu_plans_area_date_safe')
BEGIN
    DROP INDEX IX_menu_plans_area_date_safe ON menu_plans
    PRINT '✓ 删除菜单计划重复索引'
END

PRINT ''
PRINT '========================================'
PRINT '索引清理完成！'
PRINT '========================================'
PRINT '保留的关键索引：'
PRINT '• IX_food_samples_area_date_safe - 最重要的区域日期查询'
PRINT '• IX_food_samples_menu_plan_safe - 菜单计划关联查询'
PRINT '• IX_food_samples_operator_date_safe - 操作员查询'
PRINT '• IX_food_samples_created_at_safe - 分页排序'
PRINT '• IX_menu_plans_area_date_fixed - 菜单计划查询'
PRINT '• IX_users_id_name_fixed - 用户信息查询'
PRINT '• IX_administrative_areas_id_name_final - 区域信息查询'
PRINT ''
PRINT '删除的索引：'
PRINT '• 重复的日期索引'
PRINT '• 重复的综合查询索引'
PRINT '• 重复的用户、区域、菜谱索引'
PRINT ''
PRINT '预期效果：'
PRINT '• 减少索引维护开销'
PRINT '• 提高写入性能'
PRINT '• 减少存储空间占用'
PRINT '• 简化查询优化器选择'
PRINT '========================================'

-- 更新统计信息
PRINT '正在更新统计信息...'
UPDATE STATISTICS food_samples
UPDATE STATISTICS menu_plans
UPDATE STATISTICS users
UPDATE STATISTICS administrative_areas
UPDATE STATISTICS recipes
PRINT '✓ 统计信息更新完成'

PRINT ''
PRINT '🎉 索引优化完成！现在应该更快了。'
