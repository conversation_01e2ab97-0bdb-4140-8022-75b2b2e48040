-- 安全回滚脚本 - 删除我们创建的所有索引
-- 只删除我们添加的索引，不影响原有系统

PRINT '开始安全回滚，删除我们创建的索引...'
PRINT '========================================'

-- 记录开始时间
DECLARE @start_time DATETIME = GETDATE()
PRINT '回滚开始时间: ' + CONVERT(VARCHAR, @start_time, 120)

-- 1. 删除 food_samples 表的索引
PRINT ''
PRINT '1. 回滚 food_samples 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_area_date_safe')
BEGIN
    DROP INDEX IX_food_samples_area_date_safe ON food_samples
    PRINT '✓ 删除 IX_food_samples_area_date_safe'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_menu_plan_safe')
BEGIN
    DROP INDEX IX_food_samples_menu_plan_safe ON food_samples
    PRINT '✓ 删除 IX_food_samples_menu_plan_safe'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_operator_date_safe')
BEGIN
    DROP INDEX IX_food_samples_operator_date_safe ON food_samples
    PRINT '✓ 删除 IX_food_samples_operator_date_safe'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('food_samples') AND name = 'IX_food_samples_created_at_safe')
BEGIN
    DROP INDEX IX_food_samples_created_at_safe ON food_samples
    PRINT '✓ 删除 IX_food_samples_created_at_safe'
END

-- 2. 删除 weekly_menus 表的索引
PRINT ''
PRINT '2. 回滚 weekly_menus 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_optimized')
BEGIN
    DROP INDEX IX_weekly_menus_area_week_optimized ON weekly_menus
    PRINT '✓ 删除 IX_weekly_menus_area_week_optimized'
END

-- 3. 删除 weekly_menu_recipes 表的索引
PRINT ''
PRINT '3. 回滚 weekly_menu_recipes 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_menu_id')
BEGIN
    DROP INDEX IX_weekly_menu_recipes_menu_id ON weekly_menu_recipes
    PRINT '✓ 删除 IX_weekly_menu_recipes_menu_id'
END

-- 4. 删除 recipes 表的索引
PRINT ''
PRINT '4. 回滚 recipes 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('recipes') AND name = 'IX_recipes_id_name_safe')
BEGIN
    DROP INDEX IX_recipes_id_name_safe ON recipes
    PRINT '✓ 删除 IX_recipes_id_name_safe'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('recipes') AND name = 'IX_recipes_status_area_global')
BEGIN
    DROP INDEX IX_recipes_status_area_global ON recipes
    PRINT '✓ 删除 IX_recipes_status_area_global'
END

-- 5. 删除 menu_plans 表的索引
PRINT ''
PRINT '5. 回滚 menu_plans 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('menu_plans') AND name = 'IX_menu_plans_area_date_fixed')
BEGIN
    DROP INDEX IX_menu_plans_area_date_fixed ON menu_plans
    PRINT '✓ 删除 IX_menu_plans_area_date_fixed'
END

-- 6. 删除 users 表的索引
PRINT ''
PRINT '6. 回滚 users 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('users') AND name = 'IX_users_id_name_fixed')
BEGIN
    DROP INDEX IX_users_id_name_fixed ON users
    PRINT '✓ 删除 IX_users_id_name_fixed'
END

-- 7. 删除 administrative_areas 表的索引
PRINT ''
PRINT '7. 回滚 administrative_areas 表索引：'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('administrative_areas') AND name = 'IX_administrative_areas_id_name_final')
BEGIN
    DROP INDEX IX_administrative_areas_id_name_final ON administrative_areas
    PRINT '✓ 删除 IX_administrative_areas_id_name_final'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('administrative_areas') AND name = 'IX_administrative_areas_id_optimized')
BEGIN
    DROP INDEX IX_administrative_areas_id_optimized ON administrative_areas
    PRINT '✓ 删除 IX_administrative_areas_id_optimized'
END

-- 更新统计信息
PRINT ''
PRINT '正在更新统计信息...'
UPDATE STATISTICS food_samples
UPDATE STATISTICS weekly_menus
UPDATE STATISTICS weekly_menu_recipes
UPDATE STATISTICS recipes
UPDATE STATISTICS menu_plans
UPDATE STATISTICS users
UPDATE STATISTICS administrative_areas
PRINT '✓ 统计信息更新完成'

-- 记录结束时间
DECLARE @end_time DATETIME = GETDATE()
DECLARE @duration INT = DATEDIFF(second, @start_time, @end_time)

PRINT ''
PRINT '========================================'
PRINT '安全回滚完成！'
PRINT '========================================'
PRINT '回滚开始时间: ' + CONVERT(VARCHAR, @start_time, 120)
PRINT '回滚结束时间: ' + CONVERT(VARCHAR, @end_time, 120)
PRINT '总耗时: ' + CAST(@duration AS VARCHAR) + ' 秒'
PRINT ''
PRINT '✅ 已删除所有我们创建的索引'
PRINT '✅ 数据库已恢复到优化前状态'
PRINT '✅ 原有系统索引和数据完全保留'
PRINT ''
PRINT '现在数据库应该恢复到原始性能状态。'
PRINT '如果需要重新优化，请先运行健康检查脚本。'
PRINT '========================================'
